"use client";

import { useState } from "react";

interface LayoutConfig {
  rows: number;
  cols: number;
}

export default function Home() {
  const [buttons, setButtons] = useState<number[]>([]);

  const addButton = (): void => {
    setButtons((prevButtons) => [...prevButtons, prevButtons.length + 1]);
  };

  const getOptimalLayout = (count: number): LayoutConfig => {
    if (count === 0) return { rows: 0, cols: 0 };
    if (count <= 4) return { rows: count, cols: 1 };

    const cols = 2;
    const rows = Math.ceil(count / cols);
    return { rows, cols };
  };

  const layout = getOptimalLayout(buttons.length);

  const gridStyle = {
    display: 'grid' as const,
    gridTemplateColumns: `repeat(${layout.cols}, 1fr)`,
    gridTemplateRows: `repeat(${layout.rows}, 1fr)`,
  };

  const buttonStyle = {
    minHeight: '60px',
    touchAction: 'manipulation' as const,
  };

  return (
    <div className="flex flex-col min-h-screen w-full">
      <header className="h-[20vh] w-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <button
          onClick={addButton}
          className="w-[90%] h-[90%] max-h-[20vh] bg-blue-500 text-white rounded-2xl flex items-center justify-center text-6xl font-bold shadow-lg hover:bg-blue-600 active:bg-blue-700 active:scale-95 transition-all"
          aria-label="Add new button"
        >
          +
        </button>
      </header>

      <main className="h-[80vh] w-full p-2">
        {buttons.length > 0 ? (
          <div className="h-full w-full gap-2" style={gridStyle}>
            {buttons.map((num) => (
              <button
                key={num}
                className="bg-gray-200 dark:bg-gray-700 text-4xl md:text-5xl font-bold flex items-center justify-center active:bg-gray-300 dark:active:bg-gray-600 transition-colors rounded-xl shadow-md hover:shadow-lg active:scale-95"
                style={buttonStyle}
              >
                {num}
              </button>
            ))}
          </div>
        ) : (
          <div className="h-full w-full flex items-center justify-center text-gray-400 text-2xl">
            Press + to add buttons
          </div>
        )}
      </main>
    </div>
  );
}
